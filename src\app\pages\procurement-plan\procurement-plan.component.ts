import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {AuthService} from '../../services/auth.service';
import {NotificationsService} from '../shared/services/notifications.service';
import {ItemService} from '../../services/item.service';
import {ActivatedRoute, Router} from '@angular/router';
import {TranslateService} from '../shared/services/translate.service';
import {MatPaginator, MatPaginatorIntl} from '@angular/material/paginator';
import {formatPaginator} from '../shared/classes/npa-table';
import {PAGINATOR_DEFAULT_PAGE_SIZE, PAGINATOR_PAGE_SIZES} from '../../../environments/environment.const';
import {FormBuilder, FormControl, FormGroup} from '@angular/forms';
import {forkJoin, Observable} from 'rxjs';
import {map, startWith} from 'rxjs/operators';
import {IDropdownData} from '../shared/types/general.types';
import {MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';
import {CdkDragDrop, moveItemInArray} from '@angular/cdk/drag-drop';
import {ProcurementPlanService} from './procurement-plan.service';
import {HttpErrorResponse, HttpResponse} from '@angular/common/http';
import {DropdownService} from '../../services/dropdown.service';
import {FormService} from '../shared/services/form.service';
import {Location} from '@angular/common';
import { MatDialog, MatSlideToggleChange} from '@angular/material';
import {ContextAccessService} from '../../services/context-access.service';
import {IProjects} from './procurement-plan.types';
import {IContextMenuOption} from '../shared/components/context-menu/context-menu.types';
import {composeMessage} from '../shared/consts/errors';
import {RouteService} from '../../services/route.service';
import { AddEditPlanComponent } from './components/add-edit-plan/add-edit-plan.component';
import { NPA_ALERTS } from '../shared/consts/messages';
import { ConfirmationDialogComponent } from '../shared/components/confirmation-dialog/confirmation-dialog.component';

@Component({
    selector: 'app-procurement-plan',
    templateUrl: './procurement-plan.component.html',
    styleUrls: ['./procurement-plan.component.styl'],
})
export class ProcurementPlanComponent implements OnInit {
    @ViewChild(MatPaginator, {static: false}) paginator: MatPaginator;
    @ViewChild('entityInput', {static: false}) entityInput: ElementRef<HTMLInputElement>;
    @ViewChild('provinceInput', {static: false}) provinceInput: ElementRef<HTMLInputElement>;
    processes: IProjects[] = undefined;
    contextMenuOptionHeader = [];
    contextMenuOptions: IContextMenuOption[] = [];
    translatedLocation
    form: FormGroup;
    checked = false;
    isFiltered=false;
    procurementTypes: IDropdownData[] = [];
    procurementStatuses: IDropdownData[] = [];
    donors: IDropdownData[] = [];
    portfolios = [];
    sorting = [
        {
            name_prs: 'کود بودجه',
            name_ps: 'د بودجې کوډ',
            name_en: 'Budget code',
            slug: 'budget_code_slug'
        },
        {
            name_prs: 'نهاد تدارکات',
            name_ps: 'اداره',
            name_en: 'Procurement entity',
            slug: 'procurement_entity_slug'
        },
        {
            name_prs: 'ولایت',
            name_ps: 'ولایت',
            name_en: 'Province',
            slug: 'province_slug'
        },
        {
            name_prs: 'تمویل کننده',
            name_ps: 'تمویل کوونکی',
            name_en: 'Donor',
            slug: 'donor_slug'
        },
        {
            name_prs: 'حالت فعلی',
            name_ps: 'حالت',
            name_en: 'Current status',
            slug: 'status_slug'
        }
    ];
    projectStatus = [
        {
            name_prs: 'پلان شده',
            name_ps: 'پلان شوی',
            name_en: 'Planned',
            slug: 'planned',
        },
        {
            name_prs: 'فسخ شده',
            name_ps: 'فسخ شوی',
            name_en: 'Cancelled',
            slug: 'cancelled',
        },
    ];
    provinces: IDropdownData[];
    selectedProvinceItems: IDropdownData[] = [];
    provinceControl: FormControl = new FormControl();
    filteredProvinceList: Observable<IDropdownData[]>;

    procurementEntities: IDropdownData[];
    selectedProcurementEntityItems: IDropdownData[] = [];
    procurementEntityControl: FormControl = new FormControl();
    filteredProcurementEntityList: Observable<IDropdownData[]>;
    procurementMethods: IDropdownData[] = [];

    selectable = true;
    removable = true;
    formData;
    isReset = false;
    isSearched = false;
    isSelfList = undefined;
    procurement_entity:string[]=[];
    users:string[]=[];
    isExceptional:string;
    isLegalUser = false;
    user=undefined;
    entity=undefined;
    user_type='public_list';
    params;
    toggleSearchInMobileMode = false;
    yearObject: { current_year: number; year_list: number[] };
    allChecked = false;
    isChecked =false;
    processingBodies = [
        {
            id: 1,
            name_prs: 'ریاست تدارکات ملی',
            name_ps: 'د ملي تدارکاتو ریاست',
            name_en: 'National procurement authority',
            slug: 'npa'
        },
        {
            id: 2,
            name_prs: 'اداره تدارکاتی',
            name_ps: 'تدارکاتي اداره',
            name_en: 'Procurement entity',
            slug: 'pe'
        },
    ];

    projectPlanStatuses
    buttons = [];
    clickedIndex: number=2;
    isClicked = false;
    isRaisedButton: boolean = true;
    isValidUser:boolean=false;

    procurementPlanList;
    procurementPlanApprove;
    procurementPlanAdd;
    listFilter;
    planDetailsAdd;

    constructor(public authService: AuthService,
                public notificationsService: NotificationsService,
                public itemService: ItemService,
                private formBuilder: FormBuilder,
                private router: Router,
                private routeService: RouteService,
                private matPaginatorIntl: MatPaginatorIntl,
                public translateService: TranslateService,
                public dropdownService: DropdownService,
                private procurementPlanService: ProcurementPlanService,
                private location: Location,
                private activatedRoute: ActivatedRoute,
                private formService: FormService,
                public dialog: MatDialog,
                public contextAccessService: ContextAccessService) {

            this.projectPlanStatuses=procurementPlanService.projectPlanStatuses;
            this.user=this.authService.loggedInUser && this.authService.loggedInUser.username;
    }

    changeButtons(index: number) {
      this.clickedIndex = index;
      this.isRaisedButton = false;
      if(this.procurementPlanApprove){
        if(index==0 || this.clickedIndex==0){
            this.procurementEntityList();
          }
          if(index==1){
            this.publicList();
          }
      }else{
        if(index==0 || this.clickedIndex==0){
            this.myList();
          }
          if(index==1){
            this.procurementEntityList();
          }
          if(index==2){
            this.publicList();
          }
      }
    }

    ngOnInit() {
        this.routeService.toggleSearchInMobileMode.subscribe(value => {
            this.toggleSearchInMobileMode = value;
        });
        this.yearObject = this.procurementPlanService.getFiscalYear();
        this.itemService.info.next({id: undefined, data: undefined, isItemSelected: false});
        this.setDropDowns();
        this.contextMenuOptionHeader = [
            {
                title: 'آغاز طی مراحل تدارکات',
                action: (options) => {
                }
            },
            {
                title: 'ارسال اسناد و اعلان',
                action: (options) => {
                }
            },
            {
                title: 'ارسال تعدیل',
                action: (options) => {
                }
            },
            {
                title: 'اعلان تصمیم اعطای قرارداد',
                action: (options) => {
                }
            },
            {
                title: 'اطلاعیه اعطا قرارداد',
                action: (options) => {
                }
            },
        ];
        this.contextMenuOptions = [];
        this.initForm();
        setTimeout(() => {
            formatPaginator(this.matPaginatorIntl);
            this.paginator.pageSize = PAGINATOR_DEFAULT_PAGE_SIZE;
            this.paginator.pageSizeOptions = PAGINATOR_PAGE_SIZES;
            this.paginator.page.subscribe(() => {
                this.list(this.paginator.pageSize, this.paginator.pageIndex);
            });
        }, 0);

        setTimeout(() => {
            this.activatedRoute.queryParams.subscribe((queryParam) => {
                if (Object.keys(queryParam).length > 0) {
                    this.isSelfList = queryParam.list_type === 'private' && this.authService.loggedInUser !== undefined;
                }
                this.list(queryParam.page_size ? queryParam.page_size : PAGINATOR_DEFAULT_PAGE_SIZE,
                    queryParam.page_index ? queryParam.page_index : 0);
                this.paginator.pageSize = queryParam.page_size ? queryParam.page_size : PAGINATOR_DEFAULT_PAGE_SIZE;
                this.paginator.pageIndex = queryParam.page_size ? queryParam.page_index : 0;

            });
        });
        this.filteredProvinceList = this.provinceControl.valueChanges.pipe(
            startWith(''),
            map((val: string | null) => val ? this._filterProvince(val) : this.provinces)
        );

        this.filteredProcurementEntityList = this.procurementEntityControl.valueChanges.pipe(
            startWith(''),
            map((val: string | null) => val ? this._filterProcurementEntity(val) : this.procurementEntities)
        );
        this.authService.isLoggedIn.subscribe(value => {
            if (value && this.authService.singleUseToken.value) {
                this.list(PAGINATOR_DEFAULT_PAGE_SIZE, 0);
            }
        });

        this.userAuthentications();

        if(this.translateService.lang==='prs'){
            this.buttons=['خودم', 'اداره', 'عمومی'];
        }else if(this.translateService.lang==='ps'){
            this.buttons=['زما', 'اداره', 'عمومی'];
        }else{
            this.buttons=['Self', 'Entity', 'Public'];
        }
        if(this.procurementPlanApprove){
            this.buttons.shift();
            this.entity=this.authService.loggedInUser && this.authService.loggedInUser.sub_party_slug;
            this.user=null;
            this.isChecked=false;
        }


        if(this.procurementPlanApprove || this.planDetailsAdd || this.procurementPlanAdd){
            this.isValidUser=true;
        }
    }

    userAuthentications(){

        this.procurementPlanAdd = this.contextAccessService.can('procurement-plan-list','context_operation',
            ['add','view']);
        this.procurementPlanApprove = this.contextAccessService.can('procurement-plan-list','context_operation',
            ['approve', 'enter']);
        this.listFilter = this.contextAccessService.can('list-filter','context_operation',
            ['view']);
        this.planDetailsAdd = this.contextAccessService.can('plan-details-add','context_operation',
            ['view','add'])
    }

    initForm() {
        this.form = this.formBuilder.group({
            province_slug: undefined,
            procurement_entity_slug: undefined,
            procurement_type_slug: undefined,
            bidding_search: undefined,
            flow_status_slug: undefined,
            sort_criteria: undefined,
            procurement_method: undefined,
            fiscal_year: undefined,
            project_user:undefined,
            project_plan_status:undefined,
            procurement_processing_body_slug: undefined,
            latest_approved_plans:undefined
        });
    }

    list(pageSize: number, pageIndex: number, isSearched: boolean = false) {
        this.notificationsService.startLoading();
        if (!!isSearched) {
            this.paginator.pageIndex = 0;
            this.paginator.pageSize = PAGINATOR_DEFAULT_PAGE_SIZE;
        }
        const pageIndexWhenSearch = isSearched ? 0 : pageIndex;
        this.isSearched = isSearched;

        if (!isSearched) {
            this.setSearchParamsToForm();
        }
        this.formData = this.form.value;
        this.formData.list_type = this.isSelfList ? 'private' : 'public';
        this.formData.page_size = pageSize;

        this.formData.page_index = pageIndexWhenSearch;
        if (!this.form.get('fiscal_year').value) {
            this.form.get('fiscal_year').setValue([this.yearObject.current_year]);
        }
        this._prepareUrl(this.formService.generateQueryStringObject((this.formData) || {}));
        this.procurementPlanService
            .index(this.formData.page_size, this.formData.page_index, {
                self_list: this.user,
                entity_list: this.entity? this.entity :'',
                user_type: this.user_type,
                lang: this.translateService.lang,
                criteria: this.form.value,
                is_valid_user:this.isValidUser
            })
            .subscribe((response: HttpResponse<any /** type to be implemented */>) => {
                this.toggleSearchInMobileMode = false;
                this.processes = response.body[0];
                this.users= response.body[1];
                this.paginator.length = +response.headers.get('x-pagination-size');
                for (const process of this.processes) {
                    // console.log(process)
                    process.procurement_type_slug = process.procurement_type_slug;
                    process.project_cancelled=process.project_status;
                    process.procurement_type = this.dropdownService.getItem('commons', 'procurement-type', process.procurement_type_slug)['name_' + this.translateService.lang];
                    process.procurement_method = this.dropdownService.getItem('commons', 'procurement-method', process.procurement_method_slug)['name_' + this.translateService.lang];
                    process.selection_method = this.dropdownService.getItem('commons', 'selection-method', process.selection_method_slug)['name_' + this.translateService.lang];
                    process.procurement_entity_slug=process.procurement_entity_slug;
                    process.procurement_entity = this.dropdownService.getItem('commons', 'procurement-entity', process.procurement_entity_slug)['name_' + this.translateService.lang];
                    if (process.is_electronic === '1') {
                        this.translateService.get('PROCUREMENT_PLAN_LIST.ELECTRONIC_BIDDING').subscribe(
                          (value) => {
                            process.is_electronic = value;
                          }
                        );
                      } else {
                        this.translateService.get('PROCUREMENT_PLAN_LIST.NON_ELECTRONIC_BIDDING').subscribe(
                          (value) => {
                            process.is_electronic = value;
                          }
                        );
                      }

                    this.procurementPlanService.getCpvDevisions().subscribe(
                    (data) => {
                        const categories = [];
                        for (let division of data) {
                        if (process['project_category'] && process['project_category'].includes(division.code)) {
                            if (this.translateService.lang === 'prs') {
                            categories.push(division.name_da);
                            } else if (this.translateService.lang === 'ps') {
                            categories.push(division.name_pa);
                            } else {
                            categories.push(division.name_en);
                            }
                        }
                        }
                        process['project_category'] = categories.join(', ');

                    },
                    (error) => {
                        console.error(error);
                    });

                    let projectLocations = process.project_location ? process.project_location.split(',') : [];
                    let translatedLocationArray: string[] = [];

                    for (let location of projectLocations) {
                      if (process.is_foreign_location === '0') {
                        let translatedLocation = this.dropdownService.getItem('commons', 'province', location.trim());
                        if (translatedLocation) {
                          translatedLocationArray.push(translatedLocation['name_' + this.translateService.lang]);
                        }
                      } else if (process.is_foreign_location === '1') {
                          let translatedLocation = this.dropdownService.getItem('commons', 'country', location.trim());
                        if (translatedLocation) {
                          translatedLocationArray.push(translatedLocation['name_' + this.translateService.lang]);
                        }
                      }
                    }

                    process.project_location = translatedLocationArray.join(',');
            }

                this.notificationsService.dismissLoading();
            }, error => {
                this.notificationsService.error(composeMessage(error));
                console.error(error);
            });
    }

    getStatusColor(process: any) {
        const slug=process.slug;
        const requested= process['cancel_request']==='requested'||
                         process['renewal_request']==='requested'||
                         process['transfer_request']==='requested';
        if ((this.procurementPlanApprove || this.planDetailsAdd) && requested && slug === 'approved') {
            return 'chips status-type5';
        }
        if (slug === 'approved') {
            return 'chips status-type3';
        } else if (slug === 'confirmed') {
            return 'chips status-type2';
        } else if (slug === 'under-registration' || slug === 'cancelled') {
            return 'chips status-type1';
        } else if (slug === 'details-completed') {
            return 'chips status-type4';
        } else {
            return 'chips status-type3';
        }
    }


toggleAll() {
    this.allChecked = !this.allChecked;
    this.processes.forEach(item => item.checked = this.allChecked);
    this.updateCheckedStatus();
}

updateCheckedStatus() {
    this.allChecked = this.processes.every(item => item.checked);
    this.isChecked = this.processes.some(item => item.checked);
}

    selectedPlans(){
        const selectedPlans = this.processes.filter(item => item.checked);
        const projectPlanStatus = this.projectPlanStatuses[3];
        const updatedPlans = selectedPlans.map(plan => ({
            ...plan,
            project_plan_status: projectPlanStatus
        }));
        const hasNotConfirmedPlans = updatedPlans.some(confirmed => {
            return (confirmed['is_confirmed'] === '0' || confirmed['is_confirmed'] === null);
        });
        this.isClicked = true;
        this.isRaisedButton = false;
        if(hasNotConfirmedPlans){
            this.dialog.open(ConfirmationDialogComponent, {
                width: '350px',
                data: {
                    canNotCancel: true,
                    message: this.translateService.translateKey('PROCUREMENT_PLAN_LIST.NOT_APPROVED_MESSAGE'),
                    confirmText: 'COMMON.CONFIRM'
                },
                disableClose: true,
            });
        }
        else{
            this.procurementPlanService.approvePlans(updatedPlans).subscribe(
                (response) => {
                    if(response.body.includes("1") || updatedPlans[0]['project_cancelled']==='cancelled'){
                        this.dialog.open(ConfirmationDialogComponent, {
                            width: '350px',
                            data: {
                                canNotCancel: true,
                                message: this.translateService.translateKey('PROCUREMENT_PLAN_LIST.ALREADY_APPROVED_MESSAGE'),
                                confirmText: 'COMMON.CONFIRM'
                            },
                            disableClose: true,
                        });
                    }else{
                        this.notificationsService.success(NPA_ALERTS[this.translateService.lang].UPDATE_SUCCESS);
                        setTimeout(() => {
                            this.list(this.paginator.pageSize, this.paginator.pageIndex);
                        }, 1000);
                        this.isChecked=false;
                    }
                },
                (error: HttpErrorResponse) => {
                this.notificationsService.error(composeMessage(error));
                console.error(error);
                }
            );
            this.notificationsService.dismissLoading();
        }

    }

    myList(){
      this.user=this.authService.loggedInUser.username;
      this.entity=null;
      this.isChecked=false;
      this.user_type = 'my_list';
      this.refresh();
    }
    procurementEntityList(){
      this.entity=this.authService.loggedInUser && this.authService.loggedInUser.sub_party_slug;
      this.user=null;
      this.isChecked=false;
      this.user_type = 'entity_list';
      this.refresh();
    }
    publicList(){
        this.user=null;
        this.entity=null;
        this.isChecked=false;
        this.user_type = 'public_list';
        this.refresh();
    }

    setDropDowns() {
        forkJoin({
            budgetCode: this.dropdownService.getData('custom', 'sync-budget-code'),
            tenderFlowStatus: this.dropdownService.getData('references', 'tender-flow-status'),
            province: this.dropdownService.getData('commons', 'province'),
            donorSource: this.dropdownService.getData('commons', 'donor-source'),
            procurementEntity: this.dropdownService.getData('commons', 'procurement-entity'),
            procurementType: this.dropdownService.getData('commons', 'procurement-type'),
            procurementMethod: this.dropdownService.getData('commons', 'procurement-method'),
        }).subscribe((dropDownData: { [dropDownName: string]: IDropdownData[] }) => {
            this.provinces = dropDownData.province;
            this.procurementEntities = dropDownData.procurementEntity.sort(
                (a, b) => b.name_prs.localeCompare(a.name_prs) 
            );
            this.procurementTypes = dropDownData.procurementType;
            this.donors = dropDownData.donorSource;
            this.procurementMethods = dropDownData.procurementMethod;
            const maskedCriteriaVendorPublic = ['initiated', 'planned'];
            if (!this.authService.loggedInUser || (this.authService.loggedInUser && this.authService.loggedInUser.party_slug === 'vendor')) {
                this.procurementStatuses = dropDownData.tenderFlowStatus
                    .filter((status) => maskedCriteriaVendorPublic.indexOf(status.slug) === -1);
            } else {
                this.procurementStatuses = dropDownData.tenderFlowStatus;
            }
        }, (error: HttpErrorResponse) => {
            this.notificationsService.error(composeMessage(error));
            console.error(error);
        });
    }

    refresh() {
        this.list(this.paginator.pageSize, this.paginator.pageIndex, false);
    }

    loadSelfTenders(event: MatSlideToggleChange) {
        this.list(this.paginator.pageSize, this.paginator.pageIndex, false);
    }

    isValid(): boolean {
        return undefined;
    }

    selectProcurementPlan(process: any) {
        this.params = '';
        this.params = '?';
        this.params = this.location.path().split('?')[1];
        localStorage.removeItem('urlQueryStringParams');
        localStorage.setItem('urlQueryStringParams', this.params);
        const project_code = encodeURIComponent(process.project_code);
        const projectId = process.id;
        this.itemService.info
            .next(
                {
                    id: project_code,
                    data: {},
                    isItemSelected: true
                }
            );
        this.routeService.navigatedFromUrlSegment = 'procurement-plans';
        this.router.navigate(
            [`${projectId}/components`],
            {relativeTo: this.activatedRoute}
        );
    }

    submit(pageSize, pageIndex, isSearched) {
        this.isFiltered=true;
        this.list(pageSize, pageIndex, isSearched);
    }

    reset() {
        this.isReset = true;
        this.selectedProcurementEntityItems = [];
        this.selectedProvinceItems = [];
        this.form.reset();
        this.list(this.paginator.pageSize, this.paginator.pageIndex, true);
        this.router.navigateByUrl(
            `${this.translateService.lang}/${'procurement-plans'}`
        );
    }

    removeProvince(slug: string): void {
        const index = this.selectedProvinceItems.findIndex(item => item.slug === slug);
        if (index >= 0) {
            this.selectedProvinceItems.splice(index, 1);
        }
    }

    removeProcurementEntity(slug: string): void {
        const index = this.selectedProcurementEntityItems.findIndex(item => item.slug === slug);
        if (index >= 0) {
            this.selectedProcurementEntityItems.splice(index, 1);
        }
    }

    selectedProvince(event: MatAutocompleteSelectedEvent): void {
        const index = this.selectedProvinceItems.findIndex(item => item.slug === event.option.value.slug);
        if (index >= 0) {
            this.selectedProvinceItems.splice(index, 1);
        }
        this.selectedProvinceItems.push(event.option.value);
        this.provinceInput.nativeElement.value = '';
        this.provinceControl.setValue(null);
        this.form.get('province_slug').setValue(this.selectedProvinceItems);
    }

    selectedProcurementEntity(event: MatAutocompleteSelectedEvent): void {
        const index = this.selectedProcurementEntityItems.findIndex(item => item.slug === event.option.value.slug);
        if (index >= 0) {
            this.selectedProcurementEntityItems.splice(index, 1);
        }
        this.selectedProcurementEntityItems.push(event.option.value);
        this.entityInput.nativeElement.value = '';
        this.procurementEntityControl.setValue(null);
        this.form.get('procurement_entity_slug').setValue(this.selectedProcurementEntityItems);
    }

    private _filterProvince(value: string): IDropdownData[] {
        return this.provinces.filter(item => item.name_prs.includes(value));
    }

    private _filterProcurementEntity(value: string): IDropdownData[] {
        return this.procurementEntities.filter(item => item.name_prs.includes(value));
    }

    drop(event: CdkDragDrop<string[]>) {
        moveItemInArray(this.sorting, event.previousIndex, event.currentIndex);
    }

    setSearchParamsToForm() {
        this.formData = this.form.value;
        this.activatedRoute.queryParams.subscribe((urlParams) => {
            if (Object.keys(urlParams).length > 0) {
                const params = Object.assign({}, urlParams);
                for (const param in params) {
                    if (params.hasOwnProperty(param)) {
                        if (param === 'page_size' || param === 'page_index' || param === 'list_type') {
                            this.formData[param] = params[param];
                        } else if (param === 'procurement_entity_slug') {
                            const entitiesSlug = params[param].split(',');

                            for (const slug of entitiesSlug) {
                                const procurementEntity = this.dropdownService
                                    .getItem('commons', 'procurement-entity', slug);
                                this.selectedProcurementEntityItems.push(procurementEntity);
                            }
                            this.form.get(param).setValue(this.selectedProcurementEntityItems);
                        } else if (param === 'province_slug') {
                            const provincesSlug = params[param].split(',');

                            for (const slug of provincesSlug) {
                                const province = this.dropdownService
                                    .getItem('commons', 'province', slug);
                                this.selectedProvinceItems.push(province);
                            }
                            this.form.get(param).setValue(this.selectedProvinceItems);
                        } else if (param === 'procurement_type_slug') {
                            const procurementTypesSlug = params[param].split(',');
                            this.form.get(param).setValue(procurementTypesSlug);
                        } else if (param === 'flow_status_slug') {
                            const flowStatusSlug = params[param].split(',');
                            this.form.get(param).setValue(flowStatusSlug);
                        } else if (param === 'portfolio') {
                            const portfolioSlug = params[param].split(',');
                            this.form.get(param).setValue(portfolioSlug);
                        } else if (param === 'procurement_processing_body_slug') {
                            const procurementProcessingBodySlug = params[param].split(',');
                            this.form.get(param).setValue(procurementProcessingBodySlug);
                        }
                        if (param === 'fiscal_year') {
                            const fiscalYear = params[param].split(',');
                            for (let i = 0; i < fiscalYear.length; i++) {
                                fiscalYear[i] = +fiscalYear[i];
                            }
                            this.form.get(param).setValue(fiscalYear);
                        } else {
                            if (this.form.contains(param)) {
                                this.form.get(param).setValue(params[param]);
                            }
                        }
                    }
                }
            }
        });
    }

    private _prepareUrl(searchParams) {
        let path = this.router.url;
        const temp = path.split('?');
        if (Object.keys(searchParams).length <= 2 && this.isReset) {
            path = this.isReset ? temp[0] : path;
            this.location.replaceState(path);
            return;
        }
        this.isSearched = true;
        path = temp[0] + '?';
        for (const key in searchParams) {
            if (searchParams[key] !== null || searchParams[key] !== '') {
                const tempSlug = [];
                if (key === 'procurement_entity_slug' || key === 'province_slug') {
                    for (const param of searchParams[key]) {
                        tempSlug.push(param.slug);
                    }
                    if (searchParams[key] && searchParams[key].length !== 0) {
                        path += key + '=' + tempSlug + '&';
                    }
                } else if (key === 'procurement_type_slug' || key === 'portfolio' || key === 'fiscal_year' || key === 'flow_status_slug') {
                    if (searchParams[key] && searchParams[key].length !== 0) {
                        path += key + '=' + searchParams[key] + '&';
                    }
                } else {
                    path += key + '=' + searchParams[key] + '&';
                }
            }
        }
        this.location.replaceState(path);
    }


addNewPlan(){
    const dialogRef = this.dialog.open(AddEditPlanComponent, {
        disableClose: true,
        width: '850px',
        id: null
    });
    dialogRef.afterClosed().subscribe(status => {
        this.refresh()
    });
    }

downloadPlanExcelReport() {
    const ids = this.processes.map(plan => plan.id);
    this.notificationsService.startLoading();

    this.procurementPlanService.getProjectPlanExcelReport(ids).subscribe((response: Blob) => {
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'project.xlsx';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url); // Clean up
        this.notificationsService.dismissLoading();
    }, error => {
        console.error('Error downloading file:', error);
        this.notificationsService.dismissLoading();
    });
  }

generatePdf() {
    const data = {
        userId: this.authService.loggedInUser.username,
        procurementEntity: this.authService.loggedInUser.sub_party_slug,
        listType: this.user_type,
        isValidUser: this.isValidUser,
        criteria: this.form.value,
    };
    this.procurementPlanService.generatePdf(data).subscribe((response: Blob) => {
        const blob = new Blob([response], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'procurement-plan.pdf';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }, error => {
        console.error('Error downloading PDF:', error);
    });
  }

}
